-- Proof content:
-- 1. [Problem Restatement] Show that for every positive integer n, n^{1/n} ≤ 2 − 1/n. 2. [Key Idea] Apply the Arithmetic-Mean ≥ Geometric-Mean (AM-GM) inequality to the multiset consisting of n–1 ones and a single n; its GM is n^{1/n} and its AM is 2 − 1/n. 3. [Proof] Let the n numbers be 1, 1, …, 1 (n−1 times) and n. Arithmetic mean: A = [ (n−1)·1 + n ] / n = (2n−1)/n = 2 − 1/n. Geometric mean: G = (1^{\,n−1} · n)^{1/n} = n^{1/n}. By AM-GM, G ≤ A, hence n^{1/n} ≤ 2 − 1/n. 4. [Conclusion] Thus n^{1/n} never exceeds 2 − 1/n for any positive integer n, with equality only at n = 1.
