import Mathlib.Data.Real.Basic
import Mathlib.Analysis.SpecialFunctions.Pow.Real
import Mathlib.Data.Real.Sqrt

-- Theorem: |x - 1| + |x| + |x + 1| = x + 2 ⟺ 0 ≤ x ≤ 1
theorem abs_equation_iff_interval (x : ℝ) :
  |x - 1| + |x| + |x + 1| = x + 2 ↔ 0 ≤ x ∧ x ≤ 1 := by
  constructor
  · -- Forward direction: equation implies 0 ≤ x ≤ 1
    intro h
    -- First establish that |x| = x from the equation
    have lower_bound : |x - 1| + |x| + |x + 1| ≥ |x| + 2 := by
      -- Apply reverse triangle inequality: |a| + |b| ≥ |a - b|
      have triangle : |x - 1| + |x + 1| ≥ |(x - 1) - (x + 1)| := by
        rw [← abs_neg (x + 1)]
        exact abs_add_le (x - 1) (-(x + 1))
      have simplify : |(x - 1) - (x + 1)| = 2 := by
        ring_nf
        simp [abs_two]
      rw [simplify] at triangle
      -- Rearrange: |x - 1| + |x| + |x + 1| ≥ |x| + 2
      -- We have |x - 1| + |x + 1| ≥ 2, so |x - 1| + |x| + |x + 1| ≥ |x| + 2
      calc |x - 1| + |x| + |x + 1|
        = |x - 1| + |x + 1| + |x| := by ring
        _ ≥ 2 + |x| := add_le_add_right triangle |x|
        _ = |x| + 2 := by ring
    -- From equation and lower bound, get |x| = x
    have abs_eq_x : |x| = x := by
      -- From h: |x - 1| + |x| + |x + 1| = x + 2 and lower_bound: |x - 1| + |x| + |x + 1| ≥ |x| + 2
      -- We get |x| + 2 = x + 2, so |x| = x
      have : |x| + 2 ≤ x + 2 := by
        rw [← h]
        exact lower_bound
      have : |x| ≤ x := by linarith
      have : x ≤ |x| := le_abs_self x
      exact le_antisymm ‹|x| ≤ x› this
    constructor
    · -- Prove x ≥ 0
      exact abs_eq_self.mp abs_eq_x
    · -- Prove x ≤ 1
      -- Analyze equality condition in triangle inequality
      have triangle_equality : (x - 1) * (x + 1) ≤ 0 := by
        -- From the equation and the fact that |x| = x, we can derive constraints
        have x_nonneg : 0 ≤ x := abs_eq_self.mp abs_eq_x
        -- From h: |x - 1| + |x| + |x + 1| = x + 2 and |x| = x
        have : |x - 1| + x + |x + 1| = x + 2 := by rwa [abs_eq_x] at h
        have : |x - 1| + |x + 1| = 2 := by linarith
        -- Since x ≥ 0, we have x + 1 > 0, so |x + 1| = x + 1
        have pos_x_plus_1 : 0 < x + 1 := by linarith [x_nonneg]
        have : |x + 1| = x + 1 := abs_of_pos pos_x_plus_1
        rw [this] at ‹|x - 1| + |x + 1| = 2›
        have : |x - 1| = 2 - (x + 1) := by linarith [‹|x - 1| + (x + 1) = 2›]
        have : |x - 1| = 1 - x := by linarith [this]
        -- Since |x - 1| ≥ 0, we need 1 - x ≥ 0, so x ≤ 1
        have : 0 ≤ 1 - x := by rw [← ‹|x - 1| = 1 - x›]; exact abs_nonneg _
        have x_le_one : x ≤ 1 := by linarith [this]
        -- Therefore (x - 1)(x + 1) = x² - 1 ≤ 0
        -- Since x ≤ 1 and x ≥ 0, we have (x - 1)(x + 1) = x² - 1 ≤ 1 - 1 = 0
        calc (x - 1) * (x + 1)
          = x * x - 1 := by ring
          _ ≤ 1 - 1 := by
            have : x * x ≤ 1 := by
              have : x * x ≤ x * 1 := mul_le_mul_of_nonneg_left x_le_one (abs_eq_self.mp abs_eq_x)
              rw [mul_one] at this
              exact le_trans this x_le_one
            linarith [this]
          _ = 0 := by ring
      -- This gives -1 ≤ x ≤ 1
      have bound_from_triangle : -1 ≤ x ∧ x ≤ 1 := by
        -- From triangle_equality: (x - 1)(x + 1) ≤ 0, we get x² - 1 ≤ 0, so x² ≤ 1
        have x_sq_le_one : x * x ≤ 1 := by
          calc x * x
            = (x - 1) * (x + 1) + 1 := by ring
            _ ≤ 0 + 1 := by linarith [triangle_equality]
            _ = 1 := by ring
        -- From x² ≤ 1 and x ≥ 0, we get x ≤ 1
        have x_le_one_new : x ≤ 1 := by
          -- Since x ≥ 0 and x² ≤ 1, we have x ≤ √1 = 1
          have x_nonneg : 0 ≤ x := abs_eq_self.mp abs_eq_x
          rw [← sq] at x_sq_le_one
          exact le_sqrt_of_sq_le x_sq_le_one
        constructor
        · -- Prove -1 ≤ x
          have x_nonneg : 0 ≤ x := abs_eq_self.mp abs_eq_x
          linarith [x_nonneg]
        · -- Prove x ≤ 1
          exact x_le_one_new
      exact bound_from_triangle.2
  · -- Backward direction: 0 ≤ x ≤ 1 implies equation
    intro ⟨hx_nonneg, hx_le_one⟩
    -- Direct verification on [0,1]
    sorry
